import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '姓名',
    dataIndex: 'realname',
    width: 100,
    fixed: 'left',
  },
  {
    title: '性别',
    dataIndex: 'gender',
    width: 80,
    customRender: ({ text }) => {
      return text === '1' ? '男' : text === '2' ? '女' : text;
    },
  },
  {
    title: '客户单位',
    dataIndex: 'customerUnit',
    width: 150,
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 80,
  },
  {
    title: '所在城市',
    dataIndex: 'city',
    width: 120,
  },
  {
    title: '家庭住址',
    dataIndex: 'homeAddress',
    width: 200,
    ellipsis: true,
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: 180,
  },
  {
    title: '身份证有效期',
    dataIndex: 'idCardExpiry',
    width: 120,
  },
  {
    title: '手机号',
    dataIndex: 'mobile',
    width: 120,
  },
  {
    title: '学历',
    dataIndex: 'education',
    width: 100,
    customRender: ({ text }) => {
      const educationMap = {
        '1': '小学',
        '2': '初中',
        '3': '高中',
        '4': '中专',
        '5': '大专',
        '6': '本科',
        '7': '硕士',
        '8': '博士'
      };
      return educationMap[text] || text;
    },
  },
  {
    title: '政治面貌',
    dataIndex: 'politicalStatus',
    width: 120,
    customRender: ({ text }) => {
      const statusMap = {
        '1': '群众',
        '2': '团员',
        '3': '党员',
        '4': '民主党派',
        '5': '其他'
      };
      return statusMap[text] || text;
    },
  },
  {
    title: '驾驶证类型',
    dataIndex: 'licenseType',
    width: 120,
  },
  {
    title: '所在分组',
    dataIndex: 'groupName',
    width: 120,
  },
  {
    title: '驾驶证有效期',
    dataIndex: 'licenseExpiry',
    width: 120,
  },
  {
    title: '工作经验',
    dataIndex: 'workExperience',
    width: 100,
    customRender: ({ text }) => text ? `${text}年` : '',
  },
  {
    title: '驾驶员类型',
    dataIndex: 'driverType',
    width: 120,
    customRender: ({ text }) => {
      const typeMap = {
        '1': '专职司机',
        '2': '兼职司机',
        '3': '临时司机'
      };
      return typeMap[text] || text;
    },
  },
  {
    title: '紧急联系人',
    dataIndex: 'emergencyContact',
    width: 120,
  },
  {
    title: '紧急联系人电话',
    dataIndex: 'emergencyPhone',
    width: 140,
  },
  {
    title: '应聘表',
    dataIndex: 'hasApplicationForm',
    width: 80,
    customRender: ({ text }) => text === '1' ? '有' : '无',
  },
  {
    title: '入职表',
    dataIndex: 'hasOnboardingForm',
    width: 80,
    customRender: ({ text }) => text === '1' ? '有' : '无',
  },
  {
    title: '员工登记表',
    dataIndex: 'hasEmployeeForm',
    width: 100,
    customRender: ({ text }) => text === '1' ? '有' : '无',
  },
  {
    title: '路试报告',
    dataIndex: 'hasRoadTest',
    width: 90,
    customRender: ({ text }) => text === '1' ? '有' : '无',
  },
  {
    title: '胜任力测评',
    dataIndex: 'hasCompetencyTest',
    width: 100,
    customRender: ({ text }) => text === '1' ? '有' : '无',
  },
  {
    title: '心理测评',
    dataIndex: 'hasPsychTest',
    width: 90,
    customRender: ({ text }) => text === '1' ? '有' : '无',
  },
  {
    title: '体检报告',
    dataIndex: 'hasMedicalReport',
    width: 90,
    customRender: ({ text }) => text === '1' ? '有' : '无',
  },
  {
    title: '无犯罪记录',
    dataIndex: 'hasCriminalRecord',
    width: 100,
    customRender: ({ text }) => text === '1' ? '有' : '无',
  },
  {
    title: '家访',
    dataIndex: 'hasHomeVisit',
    width: 80,
    customRender: ({ text }) => text === '1' ? '有' : '无',
  },
  {
    title: '合同类型',
    dataIndex: 'contractType',
    width: 100,
    customRender: ({ text }) => {
      const contractMap = {
        '1': '劳动合同',
        '2': '劳务合同'
      };
      return contractMap[text] || text;
    },
  },
  {
    title: '司机星级',
    dataIndex: 'starLevel',
    width: 90,
    customRender: ({ text }) => {
      if (!text) return '';
      const stars = '★'.repeat(parseInt(text)) + '☆'.repeat(5 - parseInt(text));
      return `${stars} (${text}星)`;
    },
  },
  {
    title: '入职时间',
    dataIndex: 'hireDate',
    width: 120,
  },
  {
    title: '转正时间',
    dataIndex: 'regularDate',
    width: 120,
  },
  {
    title: '在职状态',
    dataIndex: 'employmentStatus',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        '1': '在职',
        '2': '离职',
        '3': '停职',
        '4': '试用期'
      };
      return statusMap[text] || text;
    },
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    width: 100,
  },
  {
    title: '操作时间',
    dataIndex: 'operateTime',
    width: 150,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '姓名',
    field: 'realname',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '身份证号',
    field: 'idCard',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '手机号',
    field: 'mobile',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '性别',
    field: 'gender',
    component: 'Select',
    componentProps: {
      options: [
        { label: '男', value: '1' },
        { label: '女', value: '2' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '客户单位',
    field: 'customerUnit',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '所在城市',
    field: 'city',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '驾驶证类型',
    field: 'licenseType',
    component: 'Select',
    componentProps: {
      options: [
        { label: 'A1', value: 'A1' },
        { label: 'A2', value: 'A2' },
        { label: 'A3', value: 'A3' },
        { label: 'B1', value: 'B1' },
        { label: 'B2', value: 'B2' },
        { label: 'C1', value: 'C1' },
        { label: 'C2', value: 'C2' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '所在分组',
    field: 'groupId',
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择分组',
      treeDefaultExpandAll: true,
      fieldNames: {
        label: 'title',
        value: 'key',
      },
    },
    colProps: { span: 6 },
  },
  {
    label: '驾驶员类型',
    field: 'driverType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '专职司机', value: '1' },
        { label: '兼职司机', value: '2' },
        { label: '临时司机', value: '3' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '在职状态',
    field: 'employmentStatus',
    component: 'Select',
    componentProps: {
      options: [
        { label: '在职', value: '1' },
        { label: '离职', value: '2' },
        { label: '停职', value: '3' },
        { label: '试用期', value: '4' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '合同类型',
    field: 'contractType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '劳动合同', value: '1' },
        { label: '劳务合同', value: '2' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '司机星级',
    field: 'starLevel',
    component: 'Select',
    componentProps: {
      options: [
        { label: '1星', value: '1' },
        { label: '2星', value: '2' },
        { label: '3星', value: '3' },
        { label: '4星', value: '4' },
        { label: '5星', value: '5' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '入职时间',
    field: 'hireDateRange',
    component: 'RangePicker',
    colProps: { span: 8 },
  },
];
